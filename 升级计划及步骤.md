# Vue 2 到 Vue 3 升级计划及步骤

## 1. 升级前准备

### 1.1 环境检查
- 确保 Node.js 版本 >= 16.0
- 检查当前项目依赖版本
- 备份当前项目代码（建议使用 git tag 或分支）
- 确保有足够的测试覆盖率来验证升级后功能

### 1.2 依赖分析
- 分析当前项目中使用的 Vue 2 特性
- 检查第三方库的 Vue 3 兼容性
- 列出需要替换或升级的插件
- 评估升级工作量和风险点

### 1.3 制定升级策略
- 渐进式升级：逐步迁移组件和功能
- 一次性升级：全面迁移（适用于小型项目）
- 混合模式：Vue 2 和 Vue 3 并存（临时方案）

## 2. 升级步骤

### 2.1 更新核心依赖

#### 2.1.1 更新 Vue 相关依赖
```bash
# 卸载 Vue 2 相关依赖
npm uninstall vue vue-router vuex

# 安装 Vue 3 相关依赖
npm install vue@latest vue-router@4 vuex@4

# 如果使用 TypeScript
npm install -D @vue/runtime-dom @vue/runtime-core
```

#### 2.1.2 更新构建工具
```bash
npm install -D @vue/cli-service@latest
npm install -D @vue/compiler-sfc@latest
```

### 2.2 更新配置文件

#### 2.2.1 更新 vue.config.js
- 检查并更新 webpack 配置
- 确保支持 Vue 3 的编译选项
- 更新构建输出配置

#### 2.2.2 更新 tsconfig.json (如果使用 TypeScript)
- 更新 TypeScript 编译选项
- 确保支持 Composition API
- 更新类型定义文件

#### 2.2.3 更新 ESLint 和 Prettier 配置（如使用）
- 更新 Vue 相关规则
- 确保支持 Composition API 语法

### 2.3 代码迁移

#### 2.3.1 入口文件更新 (main.js/main.ts)
```javascript
// Vue 2
import Vue from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'

new Vue({
  router,
  store,
  render: h => h(App)
}).$mount('#app')

// Vue 3
import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'

createApp(App)
  .use(store)
  .use(router)
  .mount('#app')
```

#### 2.3.2 组件语法更新

##### ******* 生命周期钩子变更
| Vue 2 | Vue 3 |
|-------|-------|
| beforeCreate | setup() 或 onBeforeMount |
| created | setup() 或 onMounted |
| beforeMount | onBeforeMount |
| mounted | onMounted |
| beforeUpdate | onBeforeUpdate |
| updated | onUpdated |
| beforeDestroy | onBeforeUnmount |
| destroyed | onUnmounted |

##### ******* 组件定义方式
```javascript
// Vue 2 Options API (仍然支持)
export default {
  name: 'MyComponent',
  data() {
    return {
      count: 0
    }
  },
  methods: {
    increment() {
      this.count++
    }
  }
}

// Vue 3 Composition API (推荐)
import { ref, reactive } from 'vue'

export default {
  name: 'MyComponent',
  setup() {
    const count = ref(0)
    
    const increment = () => {
      count.value++
    }
    
    return {
      count,
      increment
    }
  }
}
```

##### ******* 异步组件定义更新
```javascript
// Vue 2
const AsyncComponent = () => import('./AsyncComponent.vue')

// Vue 3
import { defineAsyncComponent } from 'vue'
const AsyncComponent = defineAsyncComponent(() => import('./AsyncComponent.vue'))
```

#### 2.3.3 Router 更新
```javascript
// Vue 2
import VueRouter from 'vue-router'

const router = new VueRouter({
  routes
})

// Vue 3
import { createRouter, createWebHistory } from 'vue-router'

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 在 main.js 中使用
// app.use(router)
```

#### 2.3.4 Vuex 更新
```javascript
// Vue 2
import Vuex from 'vuex'

const store = new Vuex.Store({
  state,
  mutations,
  actions
})

// Vue 3
import { createStore } from 'vuex'

const store = createStore({
  state,
  mutations,
  actions
})

// 在 main.js 中使用
// app.use(store)
```

#### 2.3.5 事件总线替代方案
```javascript
// Vue 2 中常用的事件总线
// Vue.prototype.$bus = new Vue()

// Vue 3 中的替代方案
import { createApp } from 'vue'
import mitt from 'mitt'

const bus = mitt()
const app = createApp({})
app.config.globalProperties.$bus = bus
```

### 2.4 特性替换

#### 2.4.1 $emit 使用更新
```javascript
// Vue 2
this.$emit('event-name', data)

// Vue 3
import { defineEmits } from 'vue'
const emit = defineEmits(['event-name'])
emit('event-name', data)
```

#### 2.4.2 插槽语法更新
```javascript
// Vue 2
this.$slots.default
this.$scopedSlots.named

// Vue 3
this.$slots.default
// scopedSlots 和 slots 合并了
```

#### 2.4.3 v-model 更新
```javascript
// Vue 2
<my-component :value="text" @input="text = $event"></my-component>
// 或
<my-component v-model="text"></my-component>

// Vue 3
<my-component :modelValue="text" @update:modelValue="text = $event"></my-component>
// 或
<my-component v-model="text"></my-component>

// 多个 v-model
<my-component v-model:first="first" v-model:last="last"></my-component>
```

#### 2.4.4 $attrs 包含 class 和 style
```javascript
// Vue 2 中 $attrs 不包含 class 和 style
// Vue 3 中 $attrs 包含 class 和 style
```

#### 2.4.5 key 属性使用变更
```html
<!-- Vue 2 中 template 上的 key 是特殊属性 -->
<template v-for="item in list" :key="item.id">...</template>

<!-- Vue 3 中需要在 v-for 的元素上放置 key -->
<template v-for="item in list">
  <div :key="item.id">...</div>
</template>
```

## 3. 常见问题及解决方案

### 3.1 插件兼容性问题
- Element UI/Plus: 确保使用 Vue 3 版本
- 其他第三方组件库需要检查兼容性
- 需要替换的库：
  - vue-class-component → vue-facing-decorator (如果使用 Class API)
  - vue-property-decorator → 新的 Composition API 方式

### 3.2 过滤器移除
```javascript
// Vue 2
{{ date | dateFormat }}

// Vue 3 - 使用方法或计算属性替代
{{ formatDate(date) }}
```

### 3.3 $children 和 $parent 移除
- 使用 provide/inject 或模板引用替代
- 使用 ref 访问子组件实例

### 3.4 特殊属性变更
- $listeners 移除（合并到 $attrs 中）
- $destroy 方法移除（由框架自动处理）

## 4. 测试验证

### 4.1 单元测试更新
- 更新测试工具配置 (Jest, Vue Test Utils)
- Vue Test Utils 从 @vue/test-utils v1 升级到 v2
- 重写依赖过时 API 的测试用例

### 4.2 功能测试
- 验证所有页面功能正常
- 检查路由跳转是否正常
- 验证状态管理是否正常工作
- 检查表单处理是否正常

### 4.3 性能测试
- 对比升级前后的性能指标
- 检查页面加载速度
- 验证内存使用情况

## 5. 性能优化

### 5.1 使用 Composition API 优化组件
- 将大型组件拆分为可复用的组合式函数
- 使用 reactive 和 ref 优化响应式数据
- 利用 provide/inject 进行依赖注入

### 5.2 Tree-shaking 优化
- 确保构建工具正确配置
- 移除未使用的代码
- 使用按需导入减少打包体积

### 5.3 响应式系统优化
- 使用 shallowRef、shallowReactive 处理大型对象
- 合理使用 watch 和 watchEffect

## 6. 部署上线

### 6.1 构建测试
- 执行生产环境构建
- 检查构建产物是否正常
- 验证构建后的功能是否正常

### 6.2 灰度发布
- 先在测试环境部署验证
- 逐步切换到生产环境
- 准备回滚方案

## 7. 升级检查清单

- [ ] 依赖版本更新完成
- [ ] 入口文件改造完成
- [ ] 所有组件语法更新完成
- [ ] 路由配置更新完成
- [ ] 状态管理更新完成
- [ ] 第三方插件兼容性处理完成
- [ ] 过时 API 替换完成
- [ ] 单元测试通过
- [ ] 功能测试通过
- [ ] 性能测试通过
- [ ] 文档更新完成
- [ ] 团队成员培训完成

## 8. 参考资料

- Vue 3 官方迁移指南: https://v3-migration.vuejs.org/
- Vue 3 官方文档: https://vuejs.org/
- Vue Router 4 文档: https://router.vuejs.org/
- Vuex 4 文档: https://vuex.vuejs.org/
- Vue Test Utils 2: https://test-utils.vuejs.org/
```